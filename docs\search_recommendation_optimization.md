# 搜索推荐功能优化文档

## 概述

本次优化对 `SearchRecordLogic::findlists` 方法进行了重构，实现了基于用户登录状态的智能搜索推荐系统。

## 原功能问题

### 主要问题
1. **缺乏个性化**：所有用户看到相同的热搜关键词
2. **未利用用户行为**：忽略了用户的搜索、点击、购买历史
3. **推荐单一**：仅基于全局搜索频次推荐
4. **用户体验差**：新用户和老用户看到相同内容

## 新功能架构

### 1. 双模式推荐系统

#### 未登录用户（全局热搜模式）
- 返回平台全局热门搜索关键词
- 基于所有用户的搜索行为统计
- 包含热门商品分类和品牌名称
- 缓存2小时，提高性能

#### 已登录用户（个性化推荐模式）
- 基于用户个人行为数据生成推荐
- 多维度权重计算
- 智能降级到全局热搜
- 缓存30分钟，保持时效性

### 2. 个性化推荐策略

#### 数据来源（权重分配）
1. **用户历史搜索**（权重3.0）- 最高优先级
2. **购买历史关键词**（权重2.5）- 高优先级
3. **点击分类关键词**（权重2.0）- 中等优先级
4. **相似用户搜索**（权重1.0）- 低优先级

#### 关键词生成策略
- **搜索历史**：直接使用用户最近30天的搜索关键词
- **分类推荐**：基于用户点击商品的分类生成相关关键词
- **购买推荐**：从购买商品名称和分类中提取关键词
- **协同过滤**：推荐相似用户的热门搜索词

### 3. 智能关键词提取

#### 商品名称关键词提取
1. **优先使用阿里云NLP分词**：获得更准确的词性分析
2. **降级到简单分词**：按分隔符和停用词过滤
3. **兜底策略**：取商品名称前几个字符

#### 关键词质量控制
- 长度限制：2-8个字符
- 停用词过滤：移除无意义词汇
- 去重处理：避免重复推荐
- 数量限制：每个来源最多3个关键词

## 核心算法详解

### 个性化权重计算
```php
// 权重合并算法
$weightedKeywords = [];

// 用户历史搜索（权重最高）
foreach ($userSearchHistory as $keyword) {
    $weightedKeywords[$keyword] = ($weightedKeywords[$keyword] ?? 0) + 3.0;
}

// 基于分类的关键词（权重中等）
foreach ($categoryBasedKeywords as $keyword) {
    $weightedKeywords[$keyword] = ($weightedKeywords[$keyword] ?? 0) + 2.0;
}

// 基于购买历史的关键词（权重较高）
foreach ($purchaseBasedKeywords as $keyword) {
    $weightedKeywords[$keyword] = ($weightedKeywords[$keyword] ?? 0) + 2.5;
}

// 相似用户关键词（权重较低）
foreach ($similarUserKeywords as $keyword) {
    $weightedKeywords[$keyword] = ($weightedKeywords[$keyword] ?? 0) + 1.0;
}
```

### 相似用户发现算法
```php
// 基于共同点击商品分类找到相似用户
$similarUsers = Db::name('goods_click')
    ->alias('gc')
    ->leftJoin('goods g', 'g.id = gc.goods_id')
    ->where('g.third_cate_id', 'in', $userCategories)
    ->where('gc.user_id', '<>', $userId)
    ->group('gc.user_id')
    ->having('COUNT(*) >= 2') // 至少有2个共同点击
    ->limit(5)
    ->column('gc.user_id');
```

## 性能优化策略

### 1. 多层缓存机制
- **个性化推荐缓存**：30分钟（`personalized_search_keywords_{userId}`）
- **全局热搜缓存**：2小时（`global_hot_search_keywords_v3`）
- **用户行为数据**：通过限制查询时间范围减少数据量

### 2. 查询优化
- **时间范围限制**：搜索历史30天，点击历史30天，购买历史90天
- **数量限制**：每个维度限制查询数量，避免大数据集处理
- **索引优化**：确保相关字段有适当的数据库索引

### 3. 降级策略
- **个性化失败**：自动降级到全局热搜
- **NLP分词失败**：使用简单字符串分割
- **数据不足**：补充全局热门关键词

## 使用方法

### 基本调用
```php
use app\api\logic\SearchRecordLogic;

// 未登录用户
$globalResult = SearchRecordLogic::findlists(null);

// 已登录用户
$personalizedResult = SearchRecordLogic::findlists($userId);

// 返回格式
// [
//     'find_lists' => ['关键词1', '关键词2', ...]
// ]
```

### 在API中使用
```php
// 在搜索相关的API控制器中
public function getSearchKeywords()
{
    $userId = $this->getUserId(); // 获取当前用户ID，可能为null
    
    $result = SearchRecordLogic::findlists($userId);
    
    return $this->success('获取成功', $result);
}
```

## 监控指标

### 推荐效果指标
1. **个性化覆盖率**：有个性化推荐的用户比例
2. **关键词点击率**：推荐关键词的点击转化率
3. **搜索转化率**：通过推荐关键词搜索的转化率
4. **用户满意度**：用户对推荐关键词的反馈

### 性能指标
1. **响应时间**：推荐接口的平均响应时间
2. **缓存命中率**：缓存的有效利用率
3. **数据库查询次数**：每次推荐的数据库查询数量
4. **内存使用量**：推荐算法的内存消耗

## 测试验证

### 使用测试脚本
```bash
php test_search_recommendations.php
```

### 测试内容
1. **功能测试**：验证个性化推荐和全局热搜功能
2. **性能测试**：测试响应时间和缓存效果
3. **数据质量测试**：验证关键词提取和权重计算
4. **边界测试**：测试无数据用户和异常情况

## 配置选项

### 可调整参数
```php
// 在相关方法中可以调整的参数
$timeRanges = [
    'search_history' => 30,    // 搜索历史天数
    'click_history' => 30,     // 点击历史天数
    'purchase_history' => 90,  // 购买历史天数
];

$weights = [
    'search_history' => 3.0,   // 搜索历史权重
    'purchase_history' => 2.5, // 购买历史权重
    'category_based' => 2.0,   // 分类推荐权重
    'similar_users' => 1.0,    // 相似用户权重
];

$limits = [
    'personalized_keywords' => 10,  // 个性化关键词数量
    'global_keywords' => 15,        // 全局关键词数量
    'similar_users' => 5,           // 相似用户数量
];
```

## 后续优化方向

### 1. 算法优化
- **深度学习模型**：使用神经网络进行更精准的推荐
- **实时更新**：基于用户实时行为调整推荐
- **多目标优化**：平衡准确性、多样性、新颖性

### 2. 数据增强
- **商品标签**：利用商品标签和属性信息
- **用户画像**：构建更完整的用户兴趣模型
- **季节性因素**：考虑时间和季节对搜索的影响

### 3. 体验优化
- **搜索联想**：实时搜索建议功能
- **热词趋势**：显示关键词热度变化
- **个性化排序**：根据用户偏好调整关键词顺序

### 4. 技术优化
- **分布式缓存**：使用Redis集群提高缓存性能
- **异步处理**：后台异步更新用户推荐数据
- **A/B测试**：支持不同推荐策略的对比测试
