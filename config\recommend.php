<?php
/**
 * 推荐算法配置文件
 */

return [
    // 推荐策略权重配置
    'strategy_weights' => [
        'category_based' => 0.4,    // 基于分类推荐权重
        'semantic_based' => 0.25,   // 基于语义推荐权重
        'collaborative' => 0.2,     // 协同过滤推荐权重
        'hot_products' => 0.1,      // 热门商品推荐权重
        'new_products' => 0.05,     // 新品推荐权重
    ],

    // 推荐数量限制
    'recommendation_limits' => [
        'category_based' => 50,     // 分类推荐数量
        'semantic_based' => 30,     // 语义推荐数量
        'collaborative' => 25,      // 协同过滤推荐数量
        'hot_products' => 15,       // 热门商品推荐数量
        'new_products' => 10,       // 新品推荐数量
        'final_result' => 100,      // 最终结果数量限制
    ],

    // 用户行为数据获取配置
    'behavior_data' => [
        'search_history' => [
            'days' => 30,           // 获取最近30天的搜索历史
            'limit' => 20,          // 最多20条记录
        ],
        'click_history' => [
            'days' => 30,           // 获取最近30天的点击历史
            'limit' => 50,          // 最多50条记录
        ],
        'purchase_history' => [
            'days' => 90,           // 获取最近90天的购买历史
            'limit' => 30,          // 最多30条记录
        ],
        'collect_history' => [
            'days' => 60,           // 获取最近60天的收藏历史
            'limit' => 30,          // 最多30条记录
        ],
        'footprint_history' => [
            'days' => 30,           // 获取最近30天的浏览足迹
            'limit' => 30,          // 最多30条记录
        ],
    ],

    // 时间权重配置
    'time_weights' => [
        'recent_days' => 7,         // 近期天数（权重1.0）
        'medium_days' => 30,        // 中期天数（权重0.7）
        'long_days' => 90,          // 长期天数（权重0.3）
        'weights' => [
            'recent' => 1.0,        // 近期权重
            'medium' => 0.7,        // 中期权重
            'long' => 0.3,          // 长期权重
            'very_long' => 0.1,     // 超长期权重
        ],
    ],

    // 行为权重配置
    'behavior_weights' => [
        'purchase' => 2.0,          // 购买行为权重
        'collect' => 1.5,           // 收藏行为权重
        'click' => 1.0,             // 点击行为权重
        'search' => 1.0,            // 搜索行为权重
    ],

    // 多样性控制配置
    'diversity_control' => [
        'max_same_category' => 5,   // 同一分类最大商品数
        'max_same_shop' => 3,       // 同一店铺最大商品数
        'category_penalty' => 0.8,  // 分类重复惩罚系数
        'shop_penalty' => 0.7,      // 店铺重复惩罚系数
    ],

    // 商品评分加成配置
    'score_bonus' => [
        'sales_factor' => 1000,     // 销量因子（销量/1000作为加成）
        'max_sales_bonus' => 0.2,   // 最大销量加成
        'new_product_bonus' => 0.1, // 新品加成
        'new_product_days' => 7,    // 新品定义天数
    ],

    // 协同过滤配置
    'collaborative_filtering' => [
        'similar_users_limit' => 10,    // 相似用户数量限制
        'min_common_clicks' => 3,       // 最小共同点击数
        'similarity_factor' => 10,      // 相似度计算因子
    ],

    // 缓存配置
    'cache' => [
        'user_behavior_ttl' => 1800,    // 用户行为数据缓存时间（30分钟）
        'hot_products_ttl' => 21600,    // 热门商品缓存时间（6小时）
        'new_products_ttl' => 7200,     // 新品缓存时间（2小时）
        'recommendation_ttl' => 1800,   // 推荐结果缓存时间（30分钟）
    ],

    // NLP配置
    'nlp' => [
        'enable_aliyun_nlp' => true,    // 是否启用阿里云NLP
        'fallback_to_local' => true,    // NLP失败时是否回退到本地分词
        'min_word_length' => 2,         // 最小词长度
    ],

    // 调试配置
    'debug' => [
        'enable_logging' => true,       // 是否启用日志记录
        'log_execution_time' => true,   // 是否记录执行时间
        'log_recommendation_source' => false, // 是否记录推荐来源
    ],
];
