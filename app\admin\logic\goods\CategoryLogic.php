<?php


namespace app\admin\logic\goods;

use app\common\model\goods\Goods;
use app\common\model\goods\GoodsCategory as GoodsCategoryModel;
use app\common\model\goods\GoodsCategoryQualification;
use app\common\server\UrlServer;
use think\facade\Db;

/**
 * 平台商品分类 逻辑层
 * Class CategoryLogic
 * @package app\admin\logic\goods
 */
class CategoryLogic
{
  /**
   *  获取分类列表(所有)
   */
  public static function lists()
  {
    $lists = GoodsCategoryModel::field('id,name,pid,is_show,shop_visible,api_visible,level,image, bg_image, sort')
      ->where('del', 0)
      ->with(['qualifications' => function($query) {
        $query->where('del', 0)->where('status', 1);
      }])
      ->order('sort', 'asc')
      ->select()
      ->toArray();

    foreach ($lists as $k => $item){
      $lists[$k]['image'] = $lists[$k]['image'] ? UrlServer::getFileUrl($item['image']) : '';

      // 处理资质信息
      $qualificationNames = [];
      if (!empty($item['qualifications'])) {
        foreach ($item['qualifications'] as $qualification) {
          $qualificationNames[] = $qualification['name'];
        }
      }
      $lists[$k]['qualification_names'] = implode('、', $qualificationNames);
    }
    // 线性结构转树形结构(顶级分类树)
    $lists = linear_to_tree($lists);
    return $lists;
  }

  /**
   *  获取分类列表(二级)
   */
  public static function categoryTwoTree()
  {
    $cateogry_list = GoodsCategoryModel::with('sons')
      ->field('id,name,pid,level')
      ->where(['del' => 0, 'level' => 1])
      ->order('sort asc')
      ->select()
      ->toArray();

    return self::categoryToSelect($cateogry_list);
  }

  /**
   * Desc：将树形结构数组输出
   * @param $items  array 要输出的数组
   * @param $select_id int 已选中项
   * @return string
   */
  public static function categoryToSelect($lists, $select_id = 0)
  {
    $tree = [];
    foreach ($lists as $val) {
      $tree[$val['id']]['level'] = $val['level'];
      $tree[$val['id']]['name'] = '|----' . $val['name'];
      if ($val['sons']) {
        foreach ($val['sons'] as $val_sons) {
          $tree[$val_sons['id']]['level'] = $val_sons['level'];
          $tree[$val_sons['id']]['name'] = '|--------' . $val_sons['name'];
        }
      }
    }
    return $tree;
  }


  /**
   * 添加分类
   */
  public static function add($post)
  {
    $level = 0;
    if ($post['pid']) {
      $level = GoodsCategoryModel::where(['id' => $post['pid']], ['del' => 0])->value('level');
    }

    $data = [
      'name'              => trim($post['name']),
      'pid'               => $post['pid'],
      'sort'              => $post['sort'],
      'is_show'           => $post['is_show'],
      'shop_visible'      => isset($post['shop_visible']) ? $post['shop_visible'] : 1,
      'api_visible'       => isset($post['api_visible']) ? $post['api_visible'] : 1,
      'image'             => isset($post['image']) ? clearDomain($post['image']) : '',
      'bg_image'          => isset($post['bg_image']) ? clearDomain($post['bg_image']) : '',
      'level'             => $level + 1,
      'remark'            => $post['remark'],
      'create_time'       => time(),
      'update_time'       => time(),
    ];

    $category = GoodsCategoryModel::create($data);

    return $category;
  }

  /**
   * 删除分类
   */
  public static function del($post)
  {
    return GoodsCategoryModel::update([
      'id' => $post['id'],
      'del' => 1,
      'update_time' => time(),
    ]);
  }


  /**
   * 分类详情
   */
  public static function getCategory($id)
  {
    $detail = GoodsCategoryModel::where([
      'del' => 0,
      'id' => $id
    ])->with(['qualifications' => function($query) {
      $query->where('del', 0)->where('status', 1);
    }])->find();

    if ($detail) {
      $detail['image'] = UrlServer::getFileUrl($detail['image']);
      $detail['bg_image'] =  $detail['bg_image'] ? UrlServer::getFileUrl($detail['bg_image']) : '';

      // 处理关联的资质信息
      $qualifications = [];
      if ($detail['qualifications']) {
        foreach ($detail['qualifications'] as $qualification) {
          $qualifications[] = [
            'id' => $qualification['id'],
            'name' => $qualification['name'],
            'description' => $qualification['description'],
            'valid_days_text' => $qualification['valid_days'] == 0 ? '永久有效' : $qualification['valid_days'] . '天'
          ];
        }
      }
      $detail['qualifications'] = $qualifications;
    }

    return $detail;
  }

  /**
   * 获取叶子分类的级数
   */
  public static function getCategoryLevel($category)
  {
    $level = 1;
    $two_ids = GoodsCategoryModel::where(['pid' => $category['id'], 'del' => 0])->column('id');
    if ($two_ids) {
      $level = 2;
      $three_id = GoodsCategoryModel::where([
        ['pid', 'in', $two_ids],
        ['del', '=', 0]
        ])->column('id');
      if ($three_id) $level = 3;
    }
    return $level;
  }

    /**
     * 编辑
     * @param $post
     * @return bool
     * @throws \Exception
     */
    public static function edit($post)
    {
        Db::startTrans();
        try {
            $category = GoodsCategoryModel::where('id', $post['id'])->find();
            if (!$category) {
                throw new \Exception('分类不存在');
            }
            $oldPid = $category->pid;

            $level = 0;
            if ($post['pid']) {
                $level = GoodsCategoryModel::where(['id' => $post['pid'], 'del' => 0])->value('level');
            }

            $data = [
                'name'          => $post['name'],
                'sort'          => $post['sort'],
                'is_show'       => $post['is_show'],
                'shop_visible'  => $post['shop_visible'] ?? 1,
                'api_visible'   => $post['api_visible'] ?? 1,
                'image'         => isset($post['image']) ? clearDomain($post['image']) : '',
                'bg_image'      => isset($post['bg_image']) ? clearDomain($post['bg_image']) : '',
                'level'         => $level + 1,
                'pid'           => $post['pid'],
                'remark'        => $post['remark'],
                'update_time'   => time(),
            ];

            $result = GoodsCategoryModel::where('id', $post['id'])->update($data);

            // 如果父级分类发生变化，则更新商品相关分类信息
            if ($result && $post['pid'] != $oldPid) {
                self::updateGoodsCategoryInfo($post['id'], $data['level']);
            }

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            // 记录日志或抛出异常
            var_dump($e->getMessage());die;
            return false;
        }
    }

    /**
     * 更新商品相关的分类信息
     * @param int $categoryId
     * @param int $newLevel
     */
    private static function updateGoodsCategoryInfo(int $categoryId, int $newLevel)
    {
        // 1. 更新所有子分类的层级
        $childIds = self::getAllChildCategoryIds($categoryId);
        $allAffectedIds = array_merge([$categoryId], $childIds);

        $categories = GoodsCategoryModel::whereIn('id', $allAffectedIds)->column('id, pid', 'id');
        $levelMap = [$categoryId => $newLevel];

        // 使用迭代方式更新层级，避免递归查询数据库
        $queue = new \SplQueue();
        $queue->enqueue($categoryId);

        while (!$queue->isEmpty()) {
            $currentId = $queue->dequeue();
            $currentLevel = $levelMap[$currentId];

            foreach ($categories as $catId => $cat) {
                if ($cat['pid'] == $currentId) {
                    $levelMap[$catId] = $currentLevel + 1;
                    GoodsCategoryModel::where('id', $catId)->update(['level' => $levelMap[$catId]]);
                    $queue->enqueue($catId);
                }
            }
        }

        // 2. 查找所有受影响的商品
        $goodsList = Goods::where(function ($query) use ($allAffectedIds) {
            $query->whereIn('first_cate_id', $allAffectedIds)
                ->whereIn('second_cate_id', $allAffectedIds, 'OR')
                ->whereIn('third_cate_id', $allAffectedIds, 'OR');
        })->select();

        // 3. 批量更新商品
        foreach ($goodsList as $goods) {
            $directCateId = $goods->third_cate_id ?: ($goods->second_cate_id ?: $goods->first_cate_id);
            if (!$directCateId) continue;

            $path = self::getCategoryPath($directCateId);
            $updateData = [
                'first_cate_id'  => $path[0] ?? 0,
                'second_cate_id' => $path[1] ?? 0,
                'third_cate_id'  => $path[2] ?? 0,
            ];
            // if(!$updateData['third_cate_id']) break;
            Goods::where('id', $goods->id)->update($updateData);
        }
    }

    /**
     * 递归获取所有子分类ID
     * @param $categoryId
     * @return array
     */
    private static function getAllChildCategoryIds($categoryId): array
    {
        $childIds = [];
        $children = GoodsCategoryModel::where('pid', $categoryId)->where('del', 0)->column('id');
        if (!empty($children)) {
            $childIds = array_merge($childIds, $children);
            foreach ($children as $childId) {
                $childIds = array_merge($childIds, self::getAllChildCategoryIds($childId));
            }
        }
        return $childIds;
    }

    /**
     * 获取分类的完整路径ID数组
     * @param $categoryId
     * @return array
     */
    private static function getCategoryPath($categoryId): array
    {
        $path = [];
        $category = GoodsCategoryModel::where('id', $categoryId)->find();
        while ($category) {
            array_unshift($path, $category->id);
            if ($category->pid == 0) {
                break;
            }
            $category = GoodsCategoryModel::where('id', $category->pid)->find();
        }
        return $path;
    }

  // 修改分类显示状态（保留兼容性）
  public static function switchStatus($post)
  {
    $update_data = [
      'is_show'       => $post['status'],
      'update_time'   => time(),
    ];
    return GoodsCategoryModel::where(['del' =>0,'id' =>$post['id']])->update($update_data);
  }

  // 切换商家端可选状态
  public static function switchShopVisible($post)
  {
    $update_data = [
      'shop_visible'  => $post['status'],
      'update_time'   => time(),
    ];
    return GoodsCategoryModel::where(['del' =>0,'id' =>$post['id']])->update($update_data);
  }

  // 切换API显示状态
  public static function switchApiVisible($post)
  {
    $update_data = [
      'api_visible'   => $post['status'],
      'update_time'   => time(),
    ];
    return GoodsCategoryModel::where(['del' =>0,'id' =>$post['id']])->update($update_data);
  }

  /**
   * 更新单个字段（内联编辑）
   */
  public static function updateField($id, $field, $value)
  {
    // 记录调试信息
    \think\facade\Log::info('CategoryLogic::updateField', [
      'id' => $id,
      'field' => $field,
      'value' => $value
    ]);

    // 允许更新的字段白名单
    $allowedFields = ['name', 'sort'];
    if (!in_array($field, $allowedFields)) {
      \think\facade\Log::error('字段不在白名单中:', $field);
      return false;
    }

    $update_data = [
      $field => $value,
      'update_time' => time(),
    ];

    $result = GoodsCategoryModel::where(['del' => 0, 'id' => $id])->update($update_data);
    \think\facade\Log::info('数据库更新结果:', ['result' => $result, 'update_data' => $update_data]);

    return $result;
  }

  /**
   * 递归排序树形结构数据 - 按层级优先级排序
   * 第一级分类排序优先级最高，然后第二级，最后第三级
   * @param array $tree 树形结构数据
   * @param string $sortField 排序字段
   * @param string $sortOrder 排序方式 asc|desc
   * @param string $subKey 子节点键名
   * @return array
   */
  public static function sortTreeBySort($tree, $sortField = 'sort', $sortOrder = 'asc', $subKey = 'sub')
  {
    if (empty($tree) || !is_array($tree)) {
      return $tree;
    }

    // 对当前层级进行排序
    usort($tree, function($a, $b) use ($sortField, $sortOrder) {
      $aSort = isset($a[$sortField]) ? (int)$a[$sortField] : 0;
      $bSort = isset($b[$sortField]) ? (int)$b[$sortField] : 0;

      // 如果sort值相同，按id排序确保稳定性
      if ($aSort === $bSort) {
        $aId = isset($a['id']) ? (int)$a['id'] : 0;
        $bId = isset($b['id']) ? (int)$b['id'] : 0;
        return $aId <=> $bId;
      }

      // 按照sort字段排序
      if ($sortOrder === 'desc') {
        return $bSort <=> $aSort;
      } else {
        return $aSort <=> $bSort;
      }
    });

    // 递归排序子节点
    // 这确保了层级优先级：先排第一级，再在每个第一级下排第二级，再在每个第二级下排第三级
    foreach ($tree as &$node) {
      if (isset($node[$subKey]) && is_array($node[$subKey]) && !empty($node[$subKey])) {
        $node[$subKey] = self::sortTreeBySort($node[$subKey], $sortField, $sortOrder, $subKey);
      }
    }

    return $tree;
  }

  /**
   * 平台商品分类（三级）
   */
  public static function categoryTreeeTree()
  {
    $lists = GoodsCategoryModel::where(['del' => 0])->column('id,name,pid,level', 'id');
    return self::cateToTree($lists, 0, '|-----', 1);
  }

  /**
   * 转树形结构
   */
  public static function cateToTree($lists, $pid = 0, $html = '|-----', $level = 1, $clear = true)
  {
    static $tree = [];
    if ($clear) $tree = [];
    foreach ($lists as $k => $v) {
        if ($v['pid'] == $pid) {
            $v['html'] = str_repeat($html, $level);
            $tree[] = $v;
            unset($lists[$k]);
            self::cateToTree($lists, $v['id'], $html, $level + 1, false);
        }
    }
    return $tree;
  }

  /**
   * 获取所有分类树形结构（用于商家端，过滤shop_visible）
   */
  public static function getAllTree()
  {
    $lists = GoodsCategoryModel::field(['name', 'id', 'pid', 'level'])
        ->where(['del' => 0, 'shop_visible' => 1])
        ->order(['sort' => 'asc'])
        ->select();
    return $lists;
  }

  /**
   * 保存分类资质关联关系
   */
  private static function saveQualificationRelations($categoryId, $qualificationIds)
  {
    // 先删除原有关联
    GoodsCategoryQualification::where('category_id', $categoryId)->delete();

    // 添加新的关联
    if (!empty($qualificationIds)) {
      $ids = is_string($qualificationIds) ? explode(',', $qualificationIds) : $qualificationIds;
      $ids = array_filter($ids); // 过滤空值

      if (!empty($ids)) {
        $data = [];
        foreach ($ids as $qualificationId) {
          $data[] = [
            'category_id' => $categoryId,
            'qualification_id' => $qualificationId,
            'create_time' => time()
          ];
        }
        GoodsCategoryQualification::insertAll($data);
      }
    }
  }
}
