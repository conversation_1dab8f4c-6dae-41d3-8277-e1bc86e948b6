-- 添加虚拟用户缓存刷新定时任务
-- 每天凌晨2点执行

INSERT INTO `ls_dev_crontab` (
    `name`, 
    `type`, 
    `remark`, 
    `command`, 
    `parameter`, 
    `status`, 
    `expression`, 
    `error`, 
    `create_time`, 
    `update_time`, 
    `last_time`, 
    `time`, 
    `max_time`, 
    `system`
) VALUES (
    '刷新虚拟用户缓存', 
    1, 
    '每天凌晨2点刷新虚拟用户缓存数据，用于商品动态展示', 
    'refresh_virtual_users_cache', 
    '', 
    1, 
    '0 2 * * *', 
    NULL, 
    UNIX_TIMESTAMP(), 
    NULL, 
    NULL, 
    '0', 
    '0', 
    1
);
