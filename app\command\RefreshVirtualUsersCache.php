<?php
namespace app\command;

use app\api\logic\GoodsDynamicLogic;
use think\console\Command;
use think\console\Input;
use think\console\Output;

/**
 * 刷新虚拟用户缓存定时任务
 * 每天2点执行，更新虚拟用户数据缓存
 */
class RefreshVirtualUsersCache extends Command
{
    protected function configure()
    {
        $this->setName('refresh:virtual-users-cache')
            ->setDescription('刷新虚拟用户缓存数据');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln('开始刷新虚拟用户缓存...');
        
        try {
            $result = GoodsDynamicLogic::refreshVirtualUsersCacheManually();
            
            if ($result) {
                $output->writeln('虚拟用户缓存刷新成功！');
                return 0;
            } else {
                $output->writeln('虚拟用户缓存刷新失败！');
                return 1;
            }
        } catch (\Exception $e) {
            $output->writeln('虚拟用户缓存刷新异常：' . $e->getMessage());
            return 1;
        }
    }
}
