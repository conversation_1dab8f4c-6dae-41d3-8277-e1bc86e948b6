<?php
namespace app\api\logic;

use app\common\basics\Logic;
use app\common\server\UrlServer;
use think\facade\Cache;
use think\facade\Db;

class GoodsDynamicLogic extends Logic
{
    private const AVATAR_API_URL = 'https://ui-avatars.com/api/?name={name}&background=random&color=fff';

    /**
     * @notes 获取商品详情页动态展示信息
     * @param int $goodsId 商品ID
     * @param int $userId 用户ID
     * @return array
     */
    public static function getDisplayInfo($goodsId, $userId)
    {
        $cacheKey = "goods_dynamic_info_{$goodsId}";
        $displayData = Cache::get($cacheKey);

        if (!$displayData) {
            $displayData = self::generateDisplayData($goodsId, $userId);

            Cache::set($cacheKey, $displayData, 79200); // 缓存2分钟
        }

        // 从缓存或生成的数据中排除当前用户
        $filteredData = array_filter($displayData['data'], function($item) use ($userId) {
            return !isset($item['user_id']) || $item['user_id'] != $userId;
        });

        // 打乱数组并返回
        shuffle($filteredData);
        return ['data'=>array_values($filteredData),'count'=>$displayData['count']];
    }

    /**
     * @notes 生成动态展示数据
     * @param int $goodsId
     * @param int $userId
     * @return array
     */
    private static function generateDisplayData($goodsId, $userId)
    {
        $displayData = [];

        // 获取商品最低价格，用于计算节省金额上限
        $goodsMinPrice = Db::name('goods')->where('id', $goodsId)->value('min_price') ?: 0;
        $maxSavedAmount = $goodsMinPrice * 0.2; // 不超过商品价格的20%

        // 1. 获取正在咨询的用户
        $consultingUsers = self::getConsultingUsers($goodsId, $userId);
        foreach ($consultingUsers as $user) {
            $displayData[] = self::formatDisplayItem(1, $user['nickname'], null, $user['id'], $user['avatar']);
        }

        // 2. 获取下单信息
        $orderGoods = self::getOrderGoods($goodsId, $userId);
        foreach ($orderGoods as $order) {
            if ($order['join_jc'] == 1) {
                $savedAmount = $order['original_price'] - $order['goods_price'];
                if ($savedAmount > 0) {
                    // 限制节省金额不超过商品价格的20%
                    $savedAmount = min($savedAmount, $maxSavedAmount);
                    $displayData[] = self::formatDisplayItem(3, $order['nickname'], $savedAmount, $order['user_id'], $order['avatar']);
                } else {
                    $displayData[] = self::formatDisplayItem(2, $order['nickname'], null, $order['user_id'], $order['avatar']);
                }
            } else {
                $displayData[] = self::formatDisplayItem(2, $order['nickname'], null, $order['user_id'], $order['avatar']);
            }
        }

        $clicks=Db::name('goods')->where('id',$goodsId)->value('clicks');
        $goods_count= $clicks*3;
        // 3. 如果数据不足，生成虚拟数据
        if (count($displayData) < 20) {
            $displayData = array_merge($displayData, self::generateDummyData(20 - count($displayData), $goodsId, $userId));
        }

        return ['data'=>$displayData,'count'=>$goods_count];
    }

    /**
     * @notes 获取咨询用户
     */
    private static function getConsultingUsers($goodsId, $userId)
    {
        return Db::name('goods_click')->alias('gc')
            ->join('user u', 'gc.user_id = u.id')
            ->where('gc.goods_id', '=', $goodsId)
            ->where('gc.user_id', '<>', $userId)
            ->field('u.id, u.nickname, u.avatar')
            ->order('gc.create_time desc')
            ->limit(10)
            ->select()
            ->toArray();
    }

    /**
     * @notes 获取订单商品
     */
    private static function getOrderGoods($goodsId, $userId)
    {
        return Db::name('order_goods')->alias('og')
            ->join('order o', 'og.order_id = o.id')
            ->join('user u', 'o.user_id = u.id')
            ->join('goods g', 'og.goods_id = g.id') // 修复：重新加入 goods 表
            ->leftJoin('goods_item gi', 'og.item_id = gi.id')
            ->where('og.goods_id', '=', $goodsId)
            ->where('o.pay_status', '=', 1)
            ->where('o.user_id', '<>', $userId)
            ->field('u.id as user_id, u.nickname, u.avatar, g.join_jc, gi.price as original_price, og.goods_price') // 修复：使用 g.join_jc
            ->order('o.create_time desc')
            ->limit(20)
            ->select()
            ->toArray();
    }
    
    /**
     * @notes 生成虚拟数据
     */
    private static function generateDummyData($count, $goodsId = null, $userId = null)
    {
        $dummyData = [];

        // 每次随机从user_xu表获取虚拟用户数据
        $virtualUsers = Db::name('user_xu')
            ->field('nickname, avatar')
            ->select()
            ->toArray();

        // 获取商品最低价格，用于计算节省金额上限
        $goodsMinPrice = 0;
        if ($goodsId) {
            $goodsMinPrice = Db::name('goods')->where('id', $goodsId)->value('max_price') ?: 0;
        }
        $maxSavedAmount = $goodsMinPrice * 0.2; // 不超过商品价格的20%

        for ($i = 0; $i < $count; $i++) {
            $type = rand(1, 3);

            // 计算节省金额，不超过商品价格的20%
            $savedAmount = null;
            if ($type === 3 && $maxSavedAmount > 0) {
                $savedAmount = rand(1, min(50, floor($maxSavedAmount))) + (rand(0, 99) / 100);
                $savedAmount = min($savedAmount, $maxSavedAmount);
            }

            // 随机选择一个虚拟用户
            $nickname = create_nickname();
            $avatar = null;

            if (!empty($virtualUsers)) {
                $randomUser = $virtualUsers[array_rand($virtualUsers)];
                $nickname = $randomUser['nickname'] ?: create_nickname();
                $avatar = $randomUser['avatar'] ? UrlServer::getFileUrl($randomUser['avatar']) : null;
            }

            $dummyData[] = self::formatDisplayItem($type, $nickname, $savedAmount, null, $avatar);
        }
        return $dummyData;
    }

    /**
     * @notes 格式化单条显示数据
     */
    private static function formatDisplayItem($type, $nickname, $savedAmount = null, $userId = null, $avatar = null)
    {
        $hiddenNickname = self::hideNickname($nickname);
        $text = '';
        switch ($type) {
            case 1:
                $text = $hiddenNickname . '正在咨询本商品，有意向拿样';
                break;
            case 2:
                $text = $hiddenNickname . '成功拿样下单本商品';
                break;
            case 3:
                $text = $hiddenNickname . '成功拿样下单，节省' . round($savedAmount, 2) . '元';
                break;
        }

        $item = [
            'type' => $type,
            'text' => $text,
            'is_vip' =>$userId?Db::name('user')->where('id',$userId)->value('jcvip'):0,
            'avatar' => !empty($avatar) ? \create_full_url($avatar) : self::generateAvatar($nickname)
        ];
        
        if ($userId !== null) {
            $item['user_id'] = $userId;
        }

        return $item;
    }

    /**
     * @notes 隐藏部分昵称
     * @param string|null $nickname
     * @return string
     */
    private static function hideNickname($nickname)
    {
        if (empty(trim($nickname)) || $nickname == 'ㅤ') {
            return mb_substr(create_nickname(), 0, 1) . '**';
        }
        
        $length = mb_strlen($nickname, 'utf-8');
        if ($length <= 1) {
            return $nickname . '***';
        }
        
        $firstChar = mb_substr($nickname, 0, 1, 'utf-8');
        $lastChar = mb_substr($nickname, -1, 1, 'utf-8');
        
        return $firstChar . str_repeat('*', min(3, $length - 1)) . $lastChar;
    }

    /**
     * @notes 根据昵称生成头像
     * @param string $nickname
     * @return string
     */
    private static function generateAvatar($nickname)
    {
        $name = mb_substr(trim($nickname), 0, 1, 'utf-8');
        if (empty($name)) {
            $name = '匿';
        }
        return str_replace('{name}', urlencode($name), self::AVATAR_API_URL);
    }

    /**
     * @notes 清除商品动态信息缓存（可用于定时任务）
     * @return bool
     */
    public static function clearGoodsDynamicCache()
    {
        try {
            // 获取所有商品ID
            $goodsIds = Db::name('goods')->column('id');

            // 清除所有商品的动态信息缓存
            foreach ($goodsIds as $goodsId) {
                $cacheKey = "goods_dynamic_info_{$goodsId}";
                Cache::delete($cacheKey);
            }

            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
}
