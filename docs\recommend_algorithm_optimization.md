# 商品推荐算法优化文档

## 概述

本次优化对 `GoodsLogic::getRecommendGoods` 方法进行了全面重构，从简单的搜索历史和点击历史推荐，升级为多维度智能推荐系统。

## 原算法问题分析

### 主要问题
1. **推荐维度单一**：仅基于搜索关键词和点击历史
2. **缺乏分类关联**：未考虑用户点击商品的分类偏好
3. **语义分析简单**：分词处理较为基础
4. **缺乏个性化**：没有用户画像和偏好分析
5. **无协同过滤**：未利用用户行为相似性
6. **缺乏多样性**：容易形成信息茧房

## 新算法架构

### 1. 多维度数据收集层
- **搜索历史**：关键词、搜索时间、搜索频次
- **点击历史**：商品ID、分类信息、点击时间
- **购买历史**：商品ID、分类信息、购买金额、购买时间
- **收藏历史**：商品ID、分类信息、收藏时间
- **浏览足迹**：商品ID、分类信息、浏览时长、浏览时间

### 2. 智能分析层
- **用户偏好分析**：基于行为数据分析用户的分类偏好和关键词偏好
- **时间权重计算**：近期行为权重更高，采用时间衰减算法
- **语义增强分析**：集成阿里云NLP进行深度语义理解

### 3. 多策略推荐层
- **基于分类推荐**（40%权重）：根据用户偏好的商品分类推荐
- **基于语义推荐**（25%权重）：基于搜索关键词的语义分析推荐
- **协同过滤推荐**（20%权重）：基于相似用户行为推荐
- **热度推荐**（10%权重）：推荐平台热门商品
- **新品推荐**（5%权重）：推荐最新上架商品

### 4. 结果优化层
- **权重合并**：将不同策略的推荐结果按权重合并
- **多样性保证**：避免同一分类或店铺商品过度集中
- **去重处理**：移除重复推荐的商品
- **个性化排序**：根据用户偏好和商品特征进行最终排序

## 核心算法详解

### 时间权重算法
```php
private static function calculateTimeWeight($actionTime, $currentTime)
{
    $daysDiff = ($currentTime - $actionTime) / (24 * 3600);
    
    if ($daysDiff <= 7) {
        return 1.0; // 近7天权重为1.0
    } elseif ($daysDiff <= 30) {
        return 0.7; // 7-30天权重为0.7
    } elseif ($daysDiff <= 90) {
        return 0.3; // 30-90天权重为0.3
    } else {
        return 0.1; // 90天以上权重为0.1
    }
}
```

### 分类偏好分析
- 从用户的点击、购买、收藏行为中提取分类偏好
- 购买行为权重 > 收藏行为权重 > 点击行为权重
- 支持一级、二级、三级分类的层级分析

### 协同过滤算法
- 基于用户行为相似性找到相似用户
- 推荐相似用户喜欢的商品
- 相似度计算基于共同点击的商品分类

### 多样性保证策略
- 同一三级分类商品数量限制
- 同一店铺商品数量限制
- 热门商品与长尾商品平衡

## 性能优化策略

### 缓存机制
- **用户行为数据缓存**：30分钟
- **用户偏好分析缓存**：2小时（通过行为数据缓存间接实现）
- **热门商品缓存**：6小时
- **新品推荐缓存**：2小时
- **推荐结果缓存**：30分钟

### 数据库优化
- 限制查询数据量，避免全表扫描
- 使用索引优化常用查询
- 分页处理大数据集

### 降级策略
- 阿里云NLP服务不可用时使用本地分词
- 推荐算法执行失败时返回随机排序
- 用户行为数据不足时推荐热门商品

## 使用方法

### 基本调用
```php
$get = [
    'user_id' => $userId,
    'keyword' => $searchKeyword // 可选
];

$sortField = GoodsLogic::getRecommendGoods($get);
```

### 在商品列表中使用
```php
// 在 getGoodsList 方法中
if($get['user_id'] && !empty($get['keyword'])) {
    $get['filed_asc'] = self::getRecommendGoods($get);
}
```

## 测试验证

使用提供的测试脚本验证算法效果：
```bash
php test_recommend_algorithm.php
```

测试内容包括：
1. 用户行为数据获取
2. 用户偏好分析
3. 各种推荐策略效果
4. 完整推荐算法性能

## 监控指标

建议监控以下指标来评估推荐效果：
1. **点击率（CTR）**：推荐商品的点击率
2. **转化率**：推荐商品的购买转化率
3. **多样性指标**：推荐结果的分类分布
4. **覆盖率**：推荐系统覆盖的商品比例
5. **响应时间**：推荐算法的执行时间

## 后续优化方向

1. **深度学习模型**：引入神经网络进行更精准的推荐
2. **实时特征工程**：实时计算用户和商品特征
3. **A/B测试框架**：支持不同推荐策略的对比测试
4. **冷启动优化**：针对新用户和新商品的推荐策略
5. **多目标优化**：平衡准确性、多样性、新颖性等多个目标
