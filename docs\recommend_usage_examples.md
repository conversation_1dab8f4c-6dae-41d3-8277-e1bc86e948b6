# 商品推荐算法使用示例

## 基本使用

### 1. 在商品列表中使用推荐算法

```php
// 在 GoodsLogic::getGoodsList 方法中
public static function getGoodsList($get)
{
    // ... 其他代码 ...
    
    // 如果用户已登录且有搜索关键词，使用推荐算法
    if($get['user_id'] && !empty($get['keyword'])) {
        $get['filed_asc'] = self::getRecommendGoods($get);
    } else {
        // 使用默认排序
        $order = [
            'sort_weight' => 'asc',
            'sort' => 'asc',
            'id' => 'desc'
        ];
    }
    
    // ... 其他代码 ...
}
```

### 2. 直接调用推荐算法

```php
use app\api\logic\GoodsLogic;

// 为特定用户获取推荐商品排序
$userId = 123;
$keyword = '手机'; // 可选

$get = [
    'user_id' => $userId,
    'keyword' => $keyword
];

$sortField = GoodsLogic::getRecommendGoods($get);

// $sortField 可能的返回值：
// 1. "FIELD(id, 1001,1002,1003,...)" - 个性化推荐排序
// 2. "RAND()" - 随机排序（降级策略）
```

## 配置自定义

### 1. 修改推荐策略权重

```php
// 在 config/recommend.php 中修改
'strategy_weights' => [
    'category_based' => 0.5,    // 增加分类推荐权重
    'semantic_based' => 0.2,    // 减少语义推荐权重
    'collaborative' => 0.15,    // 减少协同过滤权重
    'hot_products' => 0.1,      // 保持热门商品权重
    'new_products' => 0.05,     // 保持新品推荐权重
],
```

### 2. 调整推荐数量

```php
// 在 config/recommend.php 中修改
'recommendation_limits' => [
    'category_based' => 80,     // 增加分类推荐数量
    'semantic_based' => 40,     // 增加语义推荐数量
    'collaborative' => 30,      // 增加协同过滤推荐数量
    'hot_products' => 20,       // 增加热门商品推荐数量
    'new_products' => 15,       // 增加新品推荐数量
    'final_result' => 150,      // 增加最终结果数量限制
],
```

### 3. 调整时间权重

```php
// 在 config/recommend.php 中修改
'time_weights' => [
    'recent_days' => 3,         // 缩短近期天数
    'medium_days' => 15,        // 缩短中期天数
    'long_days' => 60,          // 缩短长期天数
    'weights' => [
        'recent' => 1.0,        // 保持近期权重
        'medium' => 0.6,        // 降低中期权重
        'long' => 0.2,          // 降低长期权重
        'very_long' => 0.05,    // 降低超长期权重
    ],
],
```

## 高级用法

### 1. 为不同场景使用不同配置

```php
// 创建专门的推荐方法
public static function getHomePageRecommendations($userId)
{
    // 临时修改配置，增加热门商品权重
    $originalConfig = Config::get('recommend.strategy_weights');
    Config::set('recommend.strategy_weights.hot_products', 0.3);
    Config::set('recommend.strategy_weights.category_based', 0.3);
    
    $get = ['user_id' => $userId];
    $result = self::getRecommendGoods($get);
    
    // 恢复原配置
    Config::set('recommend.strategy_weights', $originalConfig);
    
    return $result;
}
```

### 2. 监控推荐效果

```php
// 在推荐算法中添加监控
public static function getRecommendGoodsWithMonitoring($get)
{
    $startTime = microtime(true);
    
    $result = self::getRecommendGoods($get);
    
    $endTime = microtime(true);
    $executionTime = ($endTime - $startTime) * 1000;
    
    // 记录性能日志
    \think\facade\Log::info('推荐算法执行时间', [
        'user_id' => $get['user_id'],
        'execution_time' => $executionTime,
        'result_type' => is_string($result) && $result !== 'RAND()' ? 'personalized' : 'fallback'
    ]);
    
    return $result;
}
```

### 3. A/B测试不同推荐策略

```php
public static function getRecommendGoodsABTest($get)
{
    $userId = $get['user_id'];
    
    // 根据用户ID决定使用哪种策略
    $useNewAlgorithm = ($userId % 2 === 0);
    
    if ($useNewAlgorithm) {
        // 使用新的推荐算法
        return self::getRecommendGoods($get);
    } else {
        // 使用旧的推荐算法（原始版本）
        return self::getRecommendGoodsOld($get);
    }
}
```

## 性能优化建议

### 1. 预热缓存

```php
// 在系统启动时或定时任务中预热热门商品缓存
public static function warmupRecommendationCache()
{
    // 预热热门商品缓存
    self::getHotRecommendations(50);
    
    // 预热新品缓存
    self::getNewProductRecommendations(30);
    
    echo "推荐缓存预热完成\n";
}
```

### 2. 批量处理用户推荐

```php
// 为多个用户批量生成推荐
public static function batchGenerateRecommendations($userIds)
{
    $results = [];
    
    foreach ($userIds as $userId) {
        try {
            $get = ['user_id' => $userId];
            $results[$userId] = self::getRecommendGoods($get);
        } catch (\Exception $e) {
            \think\facade\Log::error("用户 {$userId} 推荐生成失败: " . $e->getMessage());
            $results[$userId] = 'RAND()';
        }
    }
    
    return $results;
}
```

## 调试和监控

### 1. 启用详细日志

```php
// 在 config/recommend.php 中启用调试
'debug' => [
    'enable_logging' => true,
    'log_execution_time' => true,
    'log_recommendation_source' => true,
],
```

### 2. 查看推荐来源

```php
// 修改 addWeight 方法以记录推荐来源
private static function addWeight($goodsIds, $weight)
{
    $config = Config::get('recommend.debug', []);
    $logSource = $config['log_recommendation_source'] ?? false;
    
    $weightedResults = [];
    foreach ($goodsIds as $goodsId) {
        $result = [
            'goods_id' => $goodsId,
            'weight' => $weight,
        ];
        
        if ($logSource) {
            $result['source'] = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 2)[1]['function'] ?? 'unknown';
        }
        
        $weightedResults[] = $result;
    }
    
    return $weightedResults;
}
```

## 常见问题解决

### 1. 推荐结果为空

```php
// 检查用户是否有足够的行为数据
$behaviorData = self::getUserBehaviorData($userId);
if (empty($behaviorData['click_history']) && empty($behaviorData['search_history'])) {
    // 新用户，使用热门商品推荐
    return self::getHotRecommendations(50);
}
```

### 2. 推荐算法执行时间过长

```php
// 设置执行时间限制
set_time_limit(5); // 5秒超时

try {
    $result = self::getRecommendGoods($get);
} catch (\Exception $e) {
    // 超时或其他错误，使用简单推荐
    return 'RAND()';
}
```

### 3. 阿里云NLP服务不可用

```php
// 在 config/recommend.php 中配置降级策略
'nlp' => [
    'enable_aliyun_nlp' => true,
    'fallback_to_local' => true,    // 启用本地分词降级
    'min_word_length' => 2,
],
```
