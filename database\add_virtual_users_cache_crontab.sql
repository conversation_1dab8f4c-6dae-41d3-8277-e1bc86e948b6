-- 添加商品动态信息缓存清除定时任务
-- 每天凌晨2点执行

INSERT INTO `ls_dev_crontab` (
    `name`,
    `type`,
    `remark`,
    `command`,
    `parameter`,
    `status`,
    `expression`,
    `error`,
    `create_time`,
    `update_time`,
    `last_time`,
    `time`,
    `max_time`,
    `system`
) VALUES (
    '清除商品动态信息缓存',
    1,
    '每天凌晨2点清除所有商品的动态信息缓存，确保虚拟数据随机性',
    'refresh_virtual_users_cache',
    '',
    1,
    '0 2 * * *',
    NULL,
    UNIX_TIMESTAMP(),
    NULL,
    NULL,
    '0',
    '0',
    1
);
